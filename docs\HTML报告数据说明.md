# A股板块数据分析HTML报告数据说明文档

## 文档概述

本文档详细说明A股板块数据分析程序生成的HTML报告中包含的所有数据内容，为第三方分析人员提供完整的数据理解和分析指导。

**报告生成程序**：A股板块数据分析程序（gupiao_bk_fenxinew）  
**报告格式**：交互式HTML报告  
**数据更新频率**：基于每日交易数据  
**文档版本**：v1.0  
**创建日期**：2025-07-24  

---

## 1. 数据概览

### 1.1 主要数据类别

HTML报告包含以下六大类数据分析：

1. **历史时间窗口分析** - 基于历史数据的板块表现分析
2. **未来时间窗口分析** - 基于历史同期数据的统计预测分析
3. **板块排名统计** - 板块在不同时间窗口的排名频次统计
4. **单日冠军统计** - 单日涨幅最高板块的统计分析
5. **历史同期分析** - 跨年度同期数据对比分析
6. **一致性分析** - 历史表现与未来预测的一致性评估

### 1.2 数据覆盖范围

- **时间跨度**：2020年5月18日至2025年7月23日（约5年历史数据）
- **板块数量**：86个A股行业板块
- **交易日数**：约1,200个交易日
- **数据粒度**：每日板块级别数据
- **分析维度**：涨跌幅、成交量、成交额、振幅、换手率等

### 1.3 数据用途

- 板块轮动分析和投资策略制定
- 历史表现回测和未来趋势预测
- 板块相对强弱评估
- 投资组合配置参考
- 市场热点板块识别

---

## 2. 具体数据项说明

### 2.1 基础数据字段

每个板块的基础数据包含以下字段：

| 字段名称 | 数据类型 | 含义说明 | 单位 |
|---------|---------|---------|------|
| sector_code | 字符串 | 板块代码（如BK0420） | - |
| sector_name | 字符串 | 板块名称（如航空机场） | - |
| date | 日期 | 交易日期 | YYYY-MM-DD |
| open | 浮点数 | 开盘指数 | 点 |
| close | 浮点数 | 收盘指数 | 点 |
| high | 浮点数 | 最高指数 | 点 |
| low | 浮点数 | 最低指数 | 点 |
| volume | 整数 | 成交量 | 股 |
| amount | 浮点数 | 成交额 | 元 |
| amplitude | 浮点数 | 振幅 | % |
| change_pct | 浮点数 | 涨跌幅 | % |
| change_amount | 浮点数 | 涨跌点数 | 点 |
| turnover_rate | 浮点数 | 换手率 | % |

### 2.2 历史时间窗口分析数据

#### 2.2.1 时间窗口设置
- **7日窗口**：短期趋势分析
- **14日窗口**：中短期趋势分析  
- **30日窗口**：月度趋势分析

#### 2.2.2 计算指标

| 指标名称 | 计算方法 | 含义说明 |
|---------|---------|---------|
| cumulative_return_pct | 累计涨跌幅 | 时间窗口内的总收益率 |
| avg_daily_change_pct | 平均日涨跌幅 | 时间窗口内日均收益率 |
| volatility | 波动率 | 收益率的标准差，衡量风险 |
| max_drawdown | 最大回撤 | 期间最大跌幅 |
| sharpe_ratio | 夏普比率 | 风险调整后收益 |

#### 2.2.3 数据时间范围
- **分析基准日期**：默认为最新交易日
- **历史回溯期**：根据时间窗口长度向前回溯
- **数据完整性**：确保每个时间窗口有足够的历史数据

### 2.3 未来时间窗口分析数据

#### 2.3.1 时间窗口设置
- **30日窗口**：月度前瞻分析
- **60日窗口**：季度前瞻分析
- **90日窗口**：季度+前瞻分析

#### 2.3.2 预测方法
- **数据基础**：仅使用2024年及之前的历史数据
- **统计方法**：基于历史同期数据的统计预测
- **样本期间**：过去5年同期数据
- **预测指标**：与历史时间窗口相同

#### 2.3.3 预测可靠性说明
- 预测基于历史统计规律，不保证未来表现
- 适用于趋势分析和相对比较
- 建议结合其他分析方法使用

### 2.4 板块排名统计数据

#### 2.4.1 排名类别
- **冠军次数**：单日涨幅第一名的次数
- **前3名次数**：进入前3名的次数  
- **前5名次数**：进入前5名的次数
- **前10名次数**：进入前10名的次数

#### 2.4.2 统计指标

| 指标名称 | 计算方法 | 含义说明 |
|---------|---------|---------|
| champion_count | 冠军次数统计 | 单日涨幅第一的天数 |
| champion_frequency_pct | 冠军频率 | 冠军次数/总交易日数 |
| avg_champion_change_pct | 平均冠军涨幅 | 获得冠军时的平均涨幅 |
| top10_count | 前10次数 | 进入前10名的天数 |
| top10_frequency_pct | 前10频率 | 前10次数/总交易日数 |

#### 2.4.3 时间范围
- **统计期间**：完整的历史数据期间
- **排名基准**：每日收盘涨跌幅
- **并列处理**：相同涨跌幅按最小排名处理

### 2.5 历史同期分析数据

#### 2.5.1 分析原理
- 选择目标日期（如7月23日）
- 查找历年同期日期的历史数据
- 计算同期时间窗口表现
- 进行跨年度对比分析

#### 2.5.2 对比维度
- **年度对比**：不同年份同期表现对比
- **平均表现**：多年同期平均表现
- **一致性分析**：同期表现的稳定性
- **异常年份识别**：表现异常的年份

#### 2.5.3 统计指标
- 各年同期累计收益率
- 多年平均收益率
- 收益率标准差
- 最佳/最差年份识别

---

## 3. 数据结构说明

### 3.1 HTML报告组织方式

HTML报告采用分层标签页结构：

```
├── 执行摘要
│   ├── 整体表现概览
│   ├── 最佳板块汇总
│   └── 一致性分析结果
├── 历史时间窗口分析
│   ├── 7日窗口标签页
│   ├── 14日窗口标签页
│   └── 30日窗口标签页
├── 未来时间窗口分析
│   ├── 30日窗口标签页
│   ├── 60日窗口标签页
│   └── 90日窗口标签页
├── 板块排名统计
│   ├── 单日冠军统计
│   ├── 前10频次统计
│   └── 多时间窗口排名
├── 历史同期分析
│   ├── 年度对比标签页
│   └── 统计汇总
└── 详细数据表格
    ├── 原始数据表
    └── 计算结果表
```

### 3.2 交互功能说明

#### 3.2.1 标签页切换
- 点击不同时间窗口标签查看对应分析结果
- 支持历史和未来时间窗口独立浏览
- 年度数据支持按年份切换查看

#### 3.2.2 数据表格功能
- 表格支持鼠标悬停高亮
- 数值自动格式化显示（百分比、千分位等）
- 正负收益率采用中国股市颜色习惯（红涨绿跌）

#### 3.2.3 响应式设计
- 支持不同屏幕尺寸自适应
- 表格支持横向滚动
- 移动设备友好显示

### 3.3 图表类型和展示形式

目前版本主要以数据表格为主，图表功能已禁用。未来版本可能包含：

- 时间序列趋势图
- 板块表现热力图  
- 排名变化动态图
- 收益率分布直方图

---

## 4. 使用说明

### 4.1 如何解读数据

#### 4.1.1 历史时间窗口分析
1. **查看排行榜**：关注累计收益率排名前列的板块
2. **风险评估**：结合波动率指标评估风险水平
3. **趋势判断**：对比不同时间窗口的表现一致性
4. **相对强弱**：横向比较各板块在同一时间窗口的表现

#### 4.1.2 未来时间窗口分析
1. **统计预测**：基于历史同期数据的统计规律
2. **趋势延续**：判断历史强势板块的延续性
3. **周期性分析**：识别具有季节性特征的板块
4. **风险提示**：注意预测的不确定性

#### 4.1.3 排名统计分析
1. **稳定性评估**：高频次进入前列的板块更稳定
2. **爆发力分析**：冠军次数多的板块具有爆发潜力
3. **持续性判断**：前10频次反映板块的持续表现能力

#### 4.1.4 一致性分析
1. **历史-未来一致性**：历史强势且未来预测也强势的板块
2. **可靠性评估**：一致性高的板块预测更可靠
3. **投资参考**：一致性分析可作为投资决策的重要参考

### 4.2 数据分析建议方向

#### 4.2.1 板块轮动分析
- 观察不同时间窗口的板块排名变化
- 识别正在轮动的热点板块
- 分析板块轮动的周期性规律

#### 4.2.2 投资策略制定
- 结合历史表现和未来预测制定投资策略
- 根据风险偏好选择合适的板块组合
- 利用排名统计数据进行择时决策

#### 4.2.3 风险管理
- 关注高波动率板块的风险
- 分析最大回撤数据评估下行风险
- 利用夏普比率进行风险调整后的收益评估

#### 4.2.4 市场趋势研判
- 通过历史同期分析判断季节性趋势
- 观察整体市场的板块分化程度
- 识别市场风格切换的信号

### 4.3 注意事项和局限性

#### 4.3.1 数据局限性
- **历史数据依赖**：分析基于历史数据，不能预测突发事件影响
- **样本期间限制**：仅覆盖约5年数据，可能不包含完整市场周期
- **板块分类固定**：板块分类基于现有标准，可能不反映最新行业变化

#### 4.3.2 预测准确性
- **统计预测**：未来时间窗口分析基于统计方法，存在不确定性
- **市场变化**：市场环境变化可能导致历史规律失效
- **外部因素**：政策变化、突发事件等外部因素未纳入考虑

#### 4.3.3 使用建议
- **综合分析**：建议结合基本面分析、技术分析等多种方法
- **动态调整**：根据市场变化及时调整分析结论
- **风险控制**：始终保持风险意识，合理控制仓位

#### 4.3.4 免责声明
- 本报告数据仅供参考，不构成投资建议
- 投资有风险，决策需谨慎
- 使用者应根据自身情况做出独立判断

---

## 5. 技术说明

### 5.1 数据来源和处理

#### 5.1.1 数据来源
- A股市场86个行业板块的每日交易数据
- 数据包含开高低收、成交量、成交额等基础指标
- 数据更新频率：每个交易日

#### 5.1.2 数据处理流程
1. **数据加载**：从CSV文件加载原始数据
2. **数据验证**：检查数据完整性和格式正确性
3. **数据清洗**：处理缺失值和异常值
4. **指标计算**：计算各种技术指标和统计指标
5. **结果输出**：生成HTML报告和CSV导出文件

### 5.2 计算方法说明

#### 5.2.1 累计收益率计算
```
累计收益率 = (期末价格 - 期初价格) / 期初价格 × 100%
```

#### 5.2.2 波动率计算
```
波动率 = 日收益率序列的标准差 × √交易日数
```

#### 5.2.3 夏普比率计算
```
夏普比率 = (平均收益率 - 无风险收益率) / 波动率
```

#### 5.2.4 最大回撤计算
```
最大回撤 = max((历史最高点 - 当前点) / 历史最高点)
```

### 5.3 程序配置参数

| 参数名称 | 默认值 | 说明 |
|---------|--------|------|
| TIME_WINDOWS | [7, 14, 30] | 历史时间窗口（天） |
| FUTURE_TIME_WINDOWS | [30, 60, 90] | 未来时间窗口（天） |
| TOP_N | 10 | 排行榜显示数量 |
| DATA_YEAR_LIMIT | 2025 | 未来分析最大年份 |

---

## 6. 更新日志

### v1.0 (2025-07-24)
- 初始版本发布
- 包含完整的数据说明和使用指导
- 涵盖所有主要功能模块的数据解释

---

## 7. 联系信息

如有疑问或需要技术支持，请联系：
- 项目维护者：AI Assistant
- 文档更新：根据程序功能变化及时更新
- 反馈渠道：通过项目代码库提交问题和建议

---

**文档结束**

*本文档为A股板块数据分析HTML报告的完整数据说明，旨在帮助第三方分析人员准确理解和有效使用报告数据。*
